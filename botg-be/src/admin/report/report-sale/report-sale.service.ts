import { Injectable } from '@nestjs/common';
import * as _ from 'lodash';
import * as moment from 'moment';
import { Decimal } from 'decimal.js';
import { InjectRepository } from '@nestjs/typeorm';
import { CrudRequest } from 'src/core/crud/crud';
import { Brackets, Repository } from 'typeorm';
import { getCustomPaginationLimit } from '../../../core/common/common.utils';
import {
  ReportAlaCarteServiceDailySaleQueryDTO,
  ReportCouponDailySaleQueryDTO,
  ReportFoodAndBeverageDailySaleQueryDTO,
  ReportMembershipDailySaleQueryDTO,
  ReportPaymentQueryDTO,
  ReportProductDailySaleQueryDTO,
  ReportSaleProfitQueryDTO,
  ReportSaleTaxQueryDTO,
  ReportServiceDailySaleQueryDTO,
  ReportTaxSummaryQueryDTO,
  ReportZQueryDTO,
} from './dto/report-sale.dto';
import { OrderDetail } from '../../order-detail/order-detail.entity';
import {
  CreditType,
  InvoiceStatus,
  ProductType,
  SaleTax,
} from '../../../core/enums/entity';
import { Invoice } from '../../invoice/invoice.entity';
import { PaymentMethod } from '../../payment-method/payment-method.entity';
import { UUID } from 'crypto';
import { InvoicePaymentMethod } from 'src/admin/invoice/invoice-payment-method.entity';
import { createPaginationReportInfo } from '../utils/paginate.utils';
import { SalesService } from 'src/admin/sales/sales.service';
import { Category } from 'src/admin/category/category.entity';

@Injectable()
export class ReportSaleService {
  constructor(
    @InjectRepository(OrderDetail)
    private orderDetailRepo: Repository<OrderDetail>,
    @InjectRepository(Invoice)
    private invoiceRepo: Repository<Invoice>,
    @InjectRepository(PaymentMethod)
    private paymentMethodRepo: Repository<PaymentMethod>,
    private readonly salesService: SalesService,
    @InjectRepository(Category)
    private categoryRepo: Repository<Category>,
  ) {}

  async getReportProductDailySale(
    req: CrudRequest,
    {
      keySearch,
      categoryId,
      startDate,
      endDate,
      clientZoneName,
    }: ReportProductDailySaleQueryDTO,
    type: ProductType,
    isExport = false,
  ) {
    const { page, limit, sort } = req?.parsed;
    const queryBuilder = this.orderDetailRepo
      .createQueryBuilder('orderDetail')
      .leftJoinAndSelect('orderDetail.order', 'order')
      .leftJoinAndSelect('order.invoice', 'invoice')
      .leftJoinAndSelect('invoice.invoiceCoupon', 'invoiceCoupon')
      .leftJoinAndSelect('invoice.customer', 'customer')
      .leftJoinAndSelect('invoice.referral', 'referral')
      .leftJoinAndSelect('invoice.invoicePayments', 'invoicePayments')
      .leftJoinAndSelect('invoicePayments.paymentMethod', 'paymentMethod')
      .leftJoinAndSelect('orderDetail.product', 'product')
      .leftJoinAndSelect('product.category', 'category')
      .leftJoinAndSelect('category.parent', 'parentCategory');

    if (
      startDate &&
      endDate &&
      moment(startDate).isValid() &&
      moment(endDate).isValid()
    ) {
      queryBuilder.andWhere('invoice.date BETWEEN :startDate AND :endDate', {
        startDate,
        endDate,
      });
    }

    if (keySearch) {
      queryBuilder.andWhere(
        new Brackets((subQuery) =>
          subQuery
            .where(
              "(CONCAT(TRIM(customer.firstName), ' ', TRIM(customer.lastName)) ILIKE :name)",
              { name: `%${keySearch}%` },
            )
            .orWhere('customer.code::text ILIKE :code', { code: keySearch })
            .orWhere('invoice.code::text ILIKE :invoiceCode', {
              invoiceCode: keySearch,
            })
            .orWhere('product.name ILIKE :productName', {
              productName: `%${keySearch}%`,
            }),
        ),
      );
    }

    if (categoryId) {
      queryBuilder.andWhere('category.id IN (:...categoryIds)', {
        categoryIds: categoryId.split(','),
      });
    }

    queryBuilder
      .andWhere('invoice.id IS NOT NULL')
      .andWhere('product.type = :type', { type })
      .andWhere('invoice.status = :status', { status: InvoiceStatus.PAID })
      .orderBy('invoice.date', 'ASC');
    const qLimit = getCustomPaginationLimit(limit);
    const offset = (page - 1) * limit || 0;
    if (!isExport) {
      queryBuilder.skip(offset).take(qLimit);
    }

    const orders = await queryBuilder.getMany();

    let sumDiscount = new Decimal(0);
    let sumQuantity = new Decimal(0);
    let sumTotal = new Decimal(0);
    let sumTax = new Decimal(0);

    const validOrders = orders.filter((ord) => {
      const paymentMethods = ord.order?.invoice?.invoicePayments || [];
      return paymentMethods.some(
        (p) =>
          !['credits', CreditType.NEW, CreditType.OLD].includes(
            p.paymentMethod.code,
          ),
      );
    });

    const outOrders = await Promise.all(
      validOrders.map(async (ord) => {
        const quantity = new Decimal(ord.quantity);
        let discount = 0;
        if (ord.order.invoice?.discount > 0) {
          discount = await this.calculateDiscountForItem(
            ord.id,
            (ord?.order?.invoice?.payload as any)?.orders[0]?.items,
            ord.order?.invoice?.discount,
          );
        }
        let tax = 0;
        tax = await this.calculateTaxForItem(
          ord.id,
          (ord?.order?.invoice?.payload as any)?.orders[0]?.items,
          ord.order?.invoice?.invoicePayments,
          ord.order?.invoice?.discount,
        );

        const totalNonCreditPaid = ord.order.invoice?.invoicePayments
          .filter(
            (payment) =>
              payment.paymentMethod.code !== CreditType.NEW &&
              payment.paymentMethod.code !== CreditType.OLD,
          )
          .reduce(
            (sum, payment) =>
              sum + (payment.paid + (payment?.roundNumber || 0)),
            0,
          );

        const nonCreditRatio =
          totalNonCreditPaid > 0
            ? totalNonCreditPaid / ord.order.invoice?.subTotal
            : 0;

        const baseAmount = ord.price;

        const nonCreditAmount = baseAmount * nonCreditRatio;

        const total = nonCreditAmount;

        const paymentMethod = ord.order.invoice?.invoicePayments.map(
          (payment) => payment.paymentMethod.name,
        );

        sumDiscount = sumDiscount.plus(discount);
        sumQuantity = sumQuantity.plus(quantity);
        sumTotal = sumTotal.plus(total);
        sumTax = sumTax.plus(tax);

        return {
          date: ord.order.invoice.date,
          referenceNo: ord.order?.invoice?.code,
          customer: ord.order?.invoice?.customer?.id
            ? {
                id: ord.order.invoice.customer?.id,
                code: ord.order.invoice.customer?.code,
                name: ord.order.invoice.customer
                  ? `${ord.order.invoice.customer?.firstName} ${ord.order.invoice.customer?.lastName}`
                  : '',
              }
            : null,
          customerCode: ord.order?.invoice?.customer?.code,
          prepaid:
            ord.order.invoice?.status === InvoiceStatus.PART_PAID
              ? 'Yes'
              : 'No',
          product: {
            id: ord.product?.id,
            name: ord.product?.name,
          },
          category: {
            id: ord.product?.category?.id,
            name: ord.product?.category?.name,
          },
          employee: {
            sale: {
              id: ord.order.invoice?.referral?.id,
              name:
                ord.order.invoice?.referral?.username ||
                ord.order.invoice?.referral?.fullname ||
                ord.order.invoice?.referral?.displayName ||
                '',
            },
          },
          paymentMethod,
          quantity,
          discount: discount.toFixed(2),
          total: total.toFixed(2),
          tax: tax.toFixed(2),
        };
      }),
    );

    if (isExport) {
      const data = [];
      let index = 0;
      for (const itemPromise of outOrders) {
        const item = await itemPromise;
        index++;
        data.push({
          order: index + 1,
          date: item.date
            ? moment.tz(item.date, clientZoneName).format('DD/MM/YYYY h:mm A')
            : '',
          customerInfo: item.customer?.code + ' ' + item.customer?.name,
          referenceNo: item.referenceNo ? `IN${item.referenceNo}` : '',
          prepaid: item.prepaid,
          productName: item.product?.name,
          quantity: item.quantity,
          discount: item.discount,
          total: item.total,
          tax: item.tax,
          paymentMethod: item.paymentMethod.join(','),
          employee: item.employee.sale?.name,
          categoryName: item.category?.name,
        });
      }
      data.push({
        order: '',
        date: '',
        customerInfo: '',
        referenceNo: '',
        prepaid: '',
        productName: '',
        categoryName: 'GRANT TOTAL:',
        quantity: sumQuantity,
        discount: sumDiscount.toFixed(2),
        total: sumTotal.toFixed(2),
        tax: sumTax.toFixed(2),
        paymentMethod: '',
        employee: '',
      });
      return data;
    }

    return {
      ...createPaginationReportInfo(outOrders, outOrders.length, limit, offset),
      sumDiscount: sumDiscount.toFixed(2),
      sumQuantity,
      sumTotal: sumTotal.toFixed(2),
      sumTax: sumTax.toFixed(2),
    };
  }

  async getReportServiceDailySale(
    req: CrudRequest,
    {
      keySearch,
      categoryId,
      startDate,
      endDate,
      clientZoneName,
    }: ReportServiceDailySaleQueryDTO,
    type: ProductType,
    isExport = false,
  ) {
    const { page, offset, limit, sort } = req?.parsed;

    const queryBuilder = this.orderDetailRepo
      .createQueryBuilder('orderDetail')
      .innerJoinAndSelect('orderDetail.order', 'order')
      .innerJoinAndSelect('order.invoice', 'invoice')
      .innerJoinAndSelect('invoice.customer', 'customer')
      .innerJoinAndSelect('invoice.referral', 'referral')
      .innerJoinAndSelect('invoice.invoicePayments', 'invoicePayments')
      .innerJoinAndSelect('invoicePayments.paymentMethod', 'paymentMethod')
      .innerJoinAndSelect('orderDetail.product', 'product')
      .innerJoinAndSelect('product.duration', 'duration')
      .innerJoinAndSelect('product.category', 'category')
      .leftJoinAndSelect('category.parent', 'parentCategory');

    if (
      startDate &&
      endDate &&
      moment(startDate).isValid() &&
      moment(endDate).isValid()
    ) {
      queryBuilder
        .andWhere('invoice.id IS NOT NULL')
        .andWhere('invoice.date BETWEEN :startDate AND :endDate', {
          startDate,
          endDate,
        });
    } else {
      queryBuilder.andWhere('invoice.id IS NOT NULL');
    }

    if (keySearch) {
      queryBuilder.andWhere(
        new Brackets((subQuery) =>
          subQuery
            .where(
              "(CONCAT(TRIM(customer.firstName), ' ', TRIM(customer.lastName)) ILIKE :name)",
              { name: `%${keySearch}%` },
            )
            .orWhere('customer.code::text ILIKE :code', { code: keySearch })
            .orWhere('invoice.code::text ILIKE :invoiceCode', {
              invoiceCode: keySearch,
            })
            .orWhere('product.name ILIKE :productName', {
              productName: `%${keySearch}%`,
            }),
        ),
      );
    }

    if (categoryId) {
      queryBuilder.andWhere('category.id IN (:...categoryIds)', {
        categoryIds: categoryId.split(','),
      });
    }

    if (type) {
      queryBuilder.andWhere('product.type = :type', { type });
    }

    queryBuilder.orderBy('invoice.date', 'ASC');

    const orders = await queryBuilder.getMany();

    let sumDiscount = new Decimal(0);
    let sumQuantity = new Decimal(0);
    let sumTotal = new Decimal(0);
    let sumTax = new Decimal(0);

    const validOrders = orders.filter((ord) => {
      const paymentMethods = ord.order?.invoice?.invoicePayments || [];
      return paymentMethods.some(
        (p) =>
          !['credits', CreditType.NEW, CreditType.OLD].includes(
            p.paymentMethod.code,
          ),
      );
    });

    const outOrders = await Promise.all(
      validOrders.map(async (ord) => {
        const quantity = new Decimal(ord.quantity);
        let discount = 0;
        if (ord.order.invoice?.discount > 0) {
          discount = await this.calculateDiscountForItem(
            ord.id,
            (ord?.order?.invoice?.payload as any)?.orders[0]?.items,
            ord.order?.invoice?.discount,
          );
        }
        let tax = 0;
        tax = await this.calculateTaxForItem(
          ord.id,
          (ord?.order?.invoice?.payload as any)?.orders[0]?.items,
          ord.order?.invoice?.invoicePayments,
          ord.order?.invoice?.discount,
        );

        const totalNonCreditPaid = ord.order.invoice?.invoicePayments
          .filter(
            (payment) =>
              payment.paymentMethod.code !== CreditType.NEW &&
              payment.paymentMethod.code !== CreditType.OLD,
          )
          .reduce(
            (sum, payment) =>
              sum + (payment.paid + (payment?.roundNumber || 0)),
            0,
          );

        const nonCreditRatio =
          totalNonCreditPaid > 0
            ? totalNonCreditPaid / ord.order.invoice?.subTotal
            : 0;

        // Adjust total and tax based on non-credit ratio
        const baseAmount = ord.price;

        const nonCreditAmount = baseAmount * nonCreditRatio;

        const total = nonCreditAmount;

        const taxRate = 0.09;

        const totalWithTax = nonCreditAmount;

        const netAmount = totalWithTax / (1 + taxRate);

        const taxAmount = totalWithTax - netAmount;

        const paymentMethod = ord.order.invoice?.invoicePayments.map(
          (payment) => payment.paymentMethod.name,
        );

        sumDiscount = sumDiscount.plus(discount);
        sumQuantity = sumQuantity.plus(quantity);
        sumTotal = sumTotal.plus(total);
        sumTax = sumTax.plus(taxAmount);

        return {
          date: ord.order.invoice.date,
          referenceNo: `IN${ord.order?.invoice?.code}`,
          customer: ord.order?.invoice?.customer?.id
            ? {
                id: ord.order.invoice.customer?.id,
                code: ord.order.invoice.customer?.code,
                name: ord.order.invoice.customer
                  ? `${ord.order.invoice.customer?.firstName} ${ord.order.invoice.customer?.lastName}`
                  : '',
              }
            : null,
          customerCode: ord.order?.invoice?.customer?.code,
          prepaid:
            ord.order.invoice?.status === InvoiceStatus.PART_PAID
              ? 'Yes'
              : 'No',
          product: {
            id: ord.product?.id,
            name: ord.product?.name,
            duration: {
              id: ord.product?.duration?.id,
              name: ord.product?.duration?.name,
            },
          },
          category: {
            id: ord.product?.category?.id,
            name: ord.product?.category?.name,
          },
          employee: {
            sale: {
              id: ord.order.invoice?.referral?.id,
              name:
                ord.order.invoice?.referral?.username ||
                ord.order.invoice?.referral?.fullname ||
                ord.order.invoice?.referral?.displayName ||
                '',
            },
          },
          paymentMethod,
          quantity,
          discount: discount.toFixed(2),
          total: total.toFixed(2),
          tax: taxAmount.toFixed(2),
        };
      }),
    );
    if (isExport) {
      const data = [];
      outOrders.forEach((item, index) => {
        data.push({
          order: index + 1,
          date: item.date
            ? moment.tz(item.date, clientZoneName).format('DD/MM/YYYY h:mm A')
            : '',
          customerInfo: item.customer?.code + ' ' + item.customer?.name,
          referenceNo: item.referenceNo,
          prepaid: item.prepaid,
          categoryName: item.category?.name,
          serviceName: item.product?.name,
          mins: item.product?.duration?.name,
          quantity: item.quantity,
          discount: item.discount,
          total: item.total,
          tax: item.tax,
          paymentMethod: item.paymentMethod.join(','),
          employee: item.employee.sale?.name,
        });
      });
      data.push({
        order: '',
        date: '',
        customerInfo: '',
        referenceNo: '',
        prepaid: '',
        categoryName: '',
        serviceName: '',
        mins: 'GRANT TOTAL:',
        quantity: sumQuantity,
        discount: sumDiscount.toFixed(2),
        total: sumTotal.toFixed(2),
        tax: sumTax.toFixed(2),
        paymentMethod: '',
        employee: '',
      });
      return data;
    }
    return {
      data: outOrders,
      sumDiscount: sumDiscount.toFixed(2),
      sumQuantity,
      sumTotal: sumTotal.toFixed(2),
      sumTax: sumTax.toFixed(2),
    };
  }

  async getReportAlaCarteServiceDailySale(
    req: CrudRequest,
    {
      keySearch,
      categoryId,
      startDate,
      endDate,
      clientZoneName,
    }: ReportAlaCarteServiceDailySaleQueryDTO,
    type: ProductType,
    isExport = false,
  ) {
    const { page, offset, limit, sort } = req?.parsed;

    const queryBuilder = this.orderDetailRepo
      .createQueryBuilder('orderDetail')
      .leftJoinAndSelect('orderDetail.employees', 'employees')
      .leftJoinAndSelect('orderDetail.order', 'order')
      .leftJoinAndSelect('order.invoice', 'invoice')
      .leftJoinAndSelect('invoice.customer', 'customer')
      .leftJoinAndSelect('invoice.referral', 'referral')
      .leftJoinAndSelect('invoice.invoicePayments', 'invoicePayments')
      .leftJoinAndSelect('invoicePayments.paymentMethod', 'paymentMethod')
      .leftJoinAndSelect('orderDetail.product', 'product')
      .leftJoinAndSelect('product.duration', 'duration')
      .leftJoinAndSelect('product.category', 'category')
      .leftJoinAndSelect('category.parent', 'parentCategory')
      .leftJoinAndSelect('invoice.invoiceCoupon', 'invoiceCoupon');

    if (
      startDate &&
      endDate &&
      moment(startDate).isValid() &&
      moment(endDate).isValid()
    ) {
      queryBuilder.andWhere('invoice.date BETWEEN :startDate AND :endDate', {
        startDate,
        endDate,
      });
    }

    if (keySearch) {
      queryBuilder.andWhere(
        new Brackets((subQuery) =>
          subQuery
            .where(
              "(CONCAT(TRIM(customer.firstName), ' ', TRIM(customer.lastName)) ILIKE :name)",
              { name: `%${keySearch}%` },
            )
            .orWhere('customer.code::text ILIKE :code', { code: keySearch })
            .orWhere('invoice.code::text ILIKE :invoiceCode', {
              invoiceCode: keySearch,
            })
            .orWhere('product.name ILIKE :productName', {
              productName: `%${keySearch}%`,
            }),
        ),
      );
    }

    if (categoryId) {
      queryBuilder.andWhere('category.id IN (:...categoryIds)', {
        categoryIds: categoryId.split(','),
      });
    }

    queryBuilder
      .andWhere('invoice.id IS NOT NULL')
      .andWhere('product.type IN (:...types)', {
        types: [
          ...(type ? [type] : []),
          ProductType.SERVICE,
          ProductType.PRODUCT,
          ProductType.FOOD_BEVERAGE,
          ProductType.FOOD,
          ProductType.BEVERAGE,
        ],
      })
      .orderBy('invoice.date', 'ASC');

    const orders = await queryBuilder.getMany();
    let sumDiscount = new Decimal(0);
    let sumQuantity = new Decimal(0);
    let sumTotal = new Decimal(0);
    let sumTax = new Decimal(0);
    let sumPrice = new Decimal(0);

    const invoiceTotals = new Map();

    orders.forEach((ord) => {
      const invoiceId = ord.order.invoice.id;
      const invoiceCode = ord.order.invoice.code;

      if (!invoiceTotals.has(invoiceId)) {
        const totalAmount = new Decimal(ord.order.invoice.total || 0);
        invoiceTotals.set(invoiceId, {
          total: totalAmount,
          code: invoiceCode,
        });
      }
    });

    const validOrders = orders.filter((ord) => {
      const paymentMethods = ord.order?.invoice?.invoicePayments || [];
      return paymentMethods.some(
        (p) =>
          !['credits', CreditType.NEW, CreditType.OLD].includes(
            p.paymentMethod.code,
          ),
      );
    });

    const outOrders = validOrders.map((ord) => {
      const quantity = new Decimal(ord.quantity || 0);
      const unitPrice = new Decimal(ord.product?.price || 0);
      const invoice = ord.order.invoice;
      const invoiceCoupon =
        Array.isArray(invoice.invoiceCoupon) && invoice.invoiceCoupon.length > 0
          ? invoice.invoiceCoupon[0]
          : null;

      // Calculate discount
      let unitDiscount = new Decimal(0);
      const allDetailDiscountZero = !ord.discount || ord.discount === 0;
      const invoiceDiscount = new Decimal(invoice.discount || 0);

      if (allDetailDiscountZero && invoiceDiscount.gt(0) && unitPrice.gt(0)) {
        if (ord.product && !ord.product.isNotApplyDiscount) {
          if (
            invoiceCoupon &&
            invoiceCoupon.couponType === 'Percentage' &&
            invoiceCoupon.percent
          ) {
            unitDiscount = unitPrice.times(invoiceCoupon.percent).div(100);
          } else {
            // Calculate total eligible items value for proportional discount
            const totalEligibleValue = orders
              .filter(
                (item) =>
                  item.order.invoice.id === invoice.id &&
                  !item.product?.isNotApplyDiscount,
              )
              .reduce(
                (sum, item) => sum.plus(new Decimal(item.product?.price || 0)),
                new Decimal(0),
              );

            if (totalEligibleValue.gt(0)) {
              unitDiscount = invoiceDiscount.times(
                unitPrice.div(totalEligibleValue),
              );
            }
          }
        }
      } else {
        unitDiscount = new Decimal(ord.discount || 0);
      }

      const totalDiscount = unitDiscount.times(quantity);
      const totalPrice = unitPrice.times(quantity);
      const lineTotalBeforeTax = totalPrice;
      // Calculate tax based on payment methods
      let taxAmount = new Decimal(0);
      let total = new Decimal(0);

      // Get total payments excluding credit
      const nonCreditPayments = ord.order.invoice.invoicePayments.filter(
        (payment) =>
          payment.paymentMethod.code !== CreditType.NEW &&
          payment.paymentMethod.code !== CreditType.OLD,
      );
      const creditPayments = ord.order.invoice.invoicePayments.filter(
        (payment) =>
          payment.paymentMethod.code === CreditType.NEW ||
          payment.paymentMethod.code === CreditType.OLD,
      );

      const totalNonCreditPaid = nonCreditPayments.reduce(
        (sum, payment) =>
          sum.plus(new Decimal(payment.paid).plus(payment.roundNumber || 0)),
        new Decimal(0),
      );
      const totalCreditPaid = creditPayments.reduce(
        (sum, payment) => sum.plus(new Decimal(payment.paid)),
        new Decimal(0),
      );

      // Calculate proportional tax for non-credit payments
      if (totalNonCreditPaid.gt(0)) {
        const taxBase = totalPrice.minus(totalDiscount).minus(totalCreditPaid);
        taxAmount = taxBase.gt(0) ? taxBase.times(0.09) : new Decimal(0);
      }

      const invoiceData = invoiceTotals.get(invoice.id);

      const invoiceItems = orders.filter(
        (item) => item.order.invoice.id === invoice.id,
      );
      const invoiceItemsTotal = invoiceItems.reduce(
        (sum, item) =>
          sum.plus(
            new Decimal(item.product?.price || 0).times(item.quantity || 0),
          ),
        new Decimal(0),
      );

      let calculatedTotal;
      if (invoiceItemsTotal.gt(0) && invoiceData) {
        const ratio = totalPrice.div(invoiceItemsTotal);
        calculatedTotal = invoiceData.total.times(ratio);
      } else {
        calculatedTotal = lineTotalBeforeTax
          .minus(totalDiscount)
          .plus(taxAmount);
      }

      total = calculatedTotal;

      // Update running totals
      sumDiscount = sumDiscount.plus(totalDiscount);
      sumQuantity = sumQuantity.plus(quantity);
      sumTotal = sumTotal.plus(total);
      sumTax = sumTax.plus(taxAmount);
      sumPrice = sumTotal;

      return {
        date: ord.order.invoice.date,
        referenceNo: ord.order?.invoice?.code,
        customer: ord.order?.invoice?.customer?.id
          ? {
              id: ord.order.invoice.customer?.id,
              code: ord.order.invoice.customer?.code,
              name: ord.order.invoice.customer
                ? `${ord.order.invoice.customer?.firstName} ${ord.order.invoice.customer?.lastName}`
                : '',
            }
          : null,
        customerCode: ord.order?.invoice?.customer?.code,
        prepaid:
          ord.order.invoice?.status === InvoiceStatus.PART_PAID ? 'Yes' : 'No',
        product: {
          id: ord.product?.id,
          name: ord.product?.name,
          price: total.toFixed(2),
          duration: {
            id: ord.product?.duration?.id,
            name: ord.product?.duration?.name,
          },
        },
        category: {
          id: ord.product?.category?.id,
          name: ord.product?.category?.name,
        },
        employee: {
          sale: {
            id: ord.order.invoice?.referral?.id,
            name:
              ord.order.invoice?.referral?.username ||
              ord.order.invoice?.referral?.fullname ||
              ord.order.invoice?.referral?.displayName ||
              '',
          },
          service: ord.employees.map((obj) => ({
            name: obj?.fullName,
            id: obj.id,
          })),
        },
        quantity,
        discount: totalDiscount.toFixed(2),
        tax: taxAmount.toFixed(2),
        total: total.toFixed(2),
        subtotal: lineTotalBeforeTax.toFixed(2),
      };
    });

    if (isExport) {
      const data = [];
      outOrders.forEach((item, index) => {
        data.push({
          order: index + 1,
          date: item.date
            ? moment.tz(item.date, clientZoneName).format('DD/MM/YYYY h:mm A')
            : '',
          customerInfo: item.customer?.code + ' ' + item.customer?.name,
          referenceNo: item.referenceNo ? `IN${item.referenceNo}` : '',
          categoryName: item.category?.name,
          serviceName: item.product?.name,
          saleEmployee: item.employee.sale?.name,
          employee: item.employee.service.map((obj) => obj.name).join(','),
          mins: item.product?.duration?.name,
          quantity: item.quantity,
          value: item.product?.price,
          discount: item.discount,
          tax: item.tax,
          total: item.total,
        });
      });
      data.push({
        order: '',
        date: '',
        customerInfo: '',
        referenceNo: '',
        categoryName: '',
        serviceName: '',
        employee: '',
        mins: 'GRANT TOTAL',
        value: sumPrice.toFixed(2),
        quantity: sumQuantity,
        discount: sumDiscount.toFixed(2),
        tax: sumTax.toFixed(2),
        total: sumTotal.toFixed(2),
      });
      return data;
    }
    return {
      data: outOrders,
      sumQuantity,
      sumPrice: sumPrice.toFixed(2),
      sumDiscount: sumDiscount.toFixed(2),
      sumTax: sumTax.toFixed(2),
      sumTotal: sumTotal.toFixed(2),
    };
  }

  async getReportMembershipDailySale(
    req: CrudRequest,
    {
      keySearch,
      categoryId,
      startDate,
      endDate,
      branchIds,
      clientZoneName,
    }: ReportMembershipDailySaleQueryDTO,
    type: ProductType,
    isExport = false,
  ) {
    const { page, limit, sort } = req?.parsed;

    let pCategoryId = [];
    if (categoryId) {
      pCategoryId = categoryId.split(',');
    }

    const queryBuilder = this.orderDetailRepo
      .createQueryBuilder('orderDetail')
      .leftJoinAndSelect('orderDetail.order', 'order')
      .leftJoinAndSelect('order.branch', 'branch')
      .leftJoinAndSelect('order.invoice', 'invoice')
      .leftJoinAndSelect('invoice.customer', 'customer')
      .leftJoinAndSelect('invoice.referral', 'referral')
      .leftJoinAndSelect('invoice.invoicePayments', 'invoicePayment')
      .leftJoinAndSelect('invoicePayment.paymentMethod', 'paymentMethod')
      .leftJoinAndSelect('orderDetail.product', 'product')
      .leftJoinAndSelect('product.category', 'category')
      .leftJoinAndSelect('category.parent', 'parent')
      .where('invoice.id IS NOT NULL')
      .andWhere(`product.type = :type`, { type });

    if (branchIds && branchIds.length > 0) {
      queryBuilder.andWhere('branch.id IN (:...branchIds)', { branchIds });
    }

    if (
      startDate &&
      endDate &&
      moment(startDate).isValid() &&
      moment(endDate).isValid()
    ) {
      queryBuilder.andWhere('invoice.date BETWEEN :startDate AND :endDate', {
        startDate,
        endDate,
      });
    }

    if (pCategoryId && pCategoryId.length) {
      queryBuilder.andWhere('category.id IN (:...pCategoryId)', {
        pCategoryId,
      });
    }

    if (keySearch) {
      queryBuilder.andWhere(
        new Brackets((subQuery) =>
          subQuery
            .where(
              "(CONCAT(TRIM(customer.firstName), ' ', TRIM(customer.lastName)) ILIKE :name)",
              { name: `%${keySearch}%` },
            )
            .orWhere('customer.code::text ILIKE :code', { code: keySearch })
            .orWhere('invoice.code::text ILIKE :invoiceCode', {
              invoiceCode: keySearch,
            })
            .orWhere('product.name ILIKE :productName', {
              productName: `%${keySearch}%`,
            }),
        ),
      );
    }

    queryBuilder.orderBy('invoice.date', 'ASC');
    const qLimit = getCustomPaginationLimit(limit);
    const offset = (page - 1) * limit || 0;
    if (!isExport) {
      queryBuilder.skip(offset).take(qLimit);
    }
    const orders = await queryBuilder.getMany();
    let sumDiscount = new Decimal(0);
    let sumQuantity = new Decimal(0);
    let sumTotal = new Decimal(0);
    let sumTax = new Decimal(0);

    const outOrders = await Promise.all(
      orders.map(async (ord) => {
        const quantity = ord.quantity;
        let discount = 0;
        if (ord.order.invoice?.discount > 0) {
          discount = await this.calculateDiscountForItem(
            ord.id,
            (ord?.order?.invoice?.payload as any)?.orders[0]?.items,
            ord.order?.invoice?.discount,
          );
        }
        let tax = 0;
        tax = (ord.product.price * quantity - discount) * 0.09;
        const total = tax == 0 ? 0 : ord.price + tax - discount;
        const paymentMethod = ord.order.invoice?.invoicePayments.map(
          (payment) => payment.paymentMethod.name,
        );
        sumDiscount = sumDiscount.plus(discount);
        sumQuantity = sumQuantity.plus(quantity);
        sumTotal = sumTotal.plus(total);
        sumTax = sumTax.plus(tax);
        return {
          date: ord.order.invoice.date
            ? moment
                .tz(ord.order.invoice.date, clientZoneName)
                .format('DD/MM/YYYY h:mm A')
            : '',
          referenceNo: ord.order?.invoice?.code,
          customer: ord.order?.invoice?.customer?.id
            ? {
                id: ord.order.invoice.customer?.id,
                code: ord.order.invoice.customer?.code,
                name: ord.order.invoice.customer
                  ? `${ord.order.invoice.customer?.firstName} ${ord.order.invoice.customer?.lastName}`
                  : '',
              }
            : null,
          prepaid:
            ord.order.invoice?.status === InvoiceStatus.PART_PAID
              ? 'Yes'
              : 'No',
          product: {
            id: ord.product?.id,
            name: ord.product?.name,
          },
          category: {
            id: ord.product?.category?.id,
            name: ord.product?.category?.name,
          },
          employee: {
            sale: {
              id: ord.order.invoice?.referral?.id,
              name:
                ord.order.invoice?.referral?.username ||
                ord.order.invoice?.referral?.fullname ||
                ord.order.invoice?.referral?.displayName ||
                '',
            },
          },
          paymentMethod,
          quantity,
          discount,
          total,
          tax,
        };
      }),
    );

    if (isExport) {
      const data = [];
      outOrders.forEach((item, index) => {
        data.push({
          order: index + 1,
          date: item.date,
          customerInfo: item.customer?.code + ' ' + item.customer?.name,
          referenceNo: `IN${item.referenceNo}`,
          prepaid: item.prepaid,
          categoryName: item.category?.name,
          membershipName: item.product?.name,
          quantity: item.quantity,
          discount: item.discount.toFixed(2),
          total: item.total.toFixed(2),
          tax: item.tax.toFixed(2),
          paymentMethod: item.paymentMethod.join(','),
          employee: item.employee.sale?.name,
        });
      });
      data.push({
        order: '',
        date: '',
        customerInfo: '',
        referenceNo: '',
        prepaid: '',
        membershipName: '',
        categoryName: 'GRANT TOTAL:',
        quantity: sumQuantity,
        discount: sumDiscount.toFixed(2),
        total: sumTotal.toFixed(2),
        tax: sumTax.toFixed(2),
        paymentMethod: '',
        employee: '',
      });
      return data;
    }

    return {
      ...createPaginationReportInfo(outOrders, outOrders.length, limit, offset),
      sumDiscount,
      sumQuantity,
      sumTotal,
      sumTax,
    };
  }

  async getReportCouponDailySale(
    req: CrudRequest,
    {
      keySearch,
      categoryId,
      startDate,
      endDate,
      branchIds,
      clientZoneName,
    }: ReportCouponDailySaleQueryDTO,
    type: ProductType,
    isExport = false,
  ) {
    const { page, offset, limit, sort } = req?.parsed;

    const queryOrders = this.orderDetailRepo
      .createQueryBuilder('orderDetail')
      .leftJoinAndSelect('orderDetail.order', 'order')
      .leftJoinAndSelect('order.branch', 'branch')
      .leftJoinAndSelect('order.invoice', 'invoice')
      .leftJoinAndSelect('invoice.customer', 'customer')
      .leftJoinAndSelect('invoice.referral', 'referral')
      .leftJoinAndSelect('invoice.invoicePayments', 'invoicePayment')
      .leftJoinAndSelect('invoicePayment.paymentMethod', 'paymentMethod')
      .leftJoinAndSelect('orderDetail.product', 'product')
      .leftJoinAndSelect('product.category', 'category')
      .leftJoinAndSelect('category.parent', 'parent')
      .where('invoice.id IS NOT NULL')
      .andWhere(`product.type = :type`, { type })
      .andWhere('orderDetail.price > 0');

    if (branchIds && branchIds.length > 0) {
      queryOrders.andWhere('branch.id IN (:...branchIds)', { branchIds });
    }

    if (categoryId) {
      queryOrders.andWhere('category.id IN (:...categoryIds)', {
        categoryIds: categoryId.split(','),
      });
    }

    if (keySearch) {
      queryOrders.andWhere(
        new Brackets((subQuery) =>
          subQuery
            .where(
              "(CONCAT(TRIM(customer.firstName), ' ', TRIM(customer.lastName)) ILIKE :name)",
              { name: `%${keySearch}%` },
            )
            .orWhere('customer.code::text ILIKE :code', { code: keySearch })
            .orWhere('invoice.code::text ILIKE :invoiceCode', {
              invoiceCode: keySearch,
            })
            .orWhere('product.name ILIKE :productName', {
              productName: `%${keySearch}%`,
            }),
        ),
      );
    }

    if (
      startDate &&
      endDate &&
      moment(startDate).isValid() &&
      moment(endDate).isValid()
    ) {
      queryOrders.andWhere('invoice.date BETWEEN :startDate AND :endDate', {
        startDate,
        endDate,
      });
    }

    queryOrders.orderBy('invoice.date', 'ASC');
    const orders = await queryOrders.getMany();

    let sumDiscount = new Decimal(0);
    let sumQuantity = new Decimal(0);
    let sumTotal = new Decimal(0);
    let sumTax = new Decimal(0);

    const outOrders = await Promise.all(
      orders.map(async (ord) => {
        const quantity = new Decimal(ord.quantity);
        let discount = 0;
        if (ord.order.invoice?.discount > 0) {
          discount = await this.calculateDiscountForItem(
            ord.id,
            (ord?.order?.invoice?.payload as any)?.orders[0]?.items,
            ord.order?.invoice?.discount,
          );
        }
        let tax = 0;
        tax = await this.calculateTaxForItem(
          ord.id,
          (ord?.order?.invoice?.payload as any)?.orders[0]?.items,
          ord.order?.invoice?.invoicePayments,
          ord.order?.invoice?.discount,
        );

        const total = tax == 0 ? 0 : ord.price + tax - discount;
        const paymentMethod = ord.order.invoice?.invoicePayments.map(
          (payment) => payment.paymentMethod.name,
        );

        sumDiscount = sumDiscount.plus(discount);
        sumQuantity = sumQuantity.plus(quantity);
        sumTotal = sumTotal.plus(total);
        sumTax = sumTax.plus(tax);

        return {
          date: ord.order.invoice.date,
          referenceNo: ord.order?.invoice?.code,
          customer: ord.order?.invoice?.customer?.id
            ? {
                id: ord.order.invoice.customer?.id,
                code: ord.order.invoice.customer?.code,
                name: ord.order.invoice.customer
                  ? `${ord.order.invoice.customer?.firstName} ${ord.order.invoice.customer?.lastName}`
                  : '',
              }
            : null,
          customerCode: ord.order?.invoice?.customer?.code,
          prepaid:
            ord.order.invoice?.status === InvoiceStatus.PART_PAID
              ? 'Yes'
              : 'No',
          product: {
            id: ord.product?.id,
            name: ord.product?.name,
          },
          category: {
            id: ord.product?.category?.id,
            name: ord.product?.category?.name,
          },
          employee: {
            sale: {
              id: ord.order.invoice?.referral?.id,
              name:
                ord.order.invoice?.referral?.username ||
                ord.order.invoice?.referral?.fullname ||
                ord.order.invoice?.referral?.displayName ||
                '',
            },
          },
          paymentMethod,
          quantity,
          discount: discount.toFixed(2),
          total: total.toFixed(2),
          tax: tax.toFixed(2),
        };
      }),
    );

    if (isExport) {
      const data = [];
      outOrders.forEach((item, index) => {
        data.push({
          order: index + 1,
          date: item.date
            ? moment.tz(item.date, clientZoneName).format('DD/MM/YYYY h:mm A')
            : '',
          customerInfo: item.customer?.code + ' ' + item.customer?.name,
          referenceNo: item.referenceNo ? `IN${item.referenceNo}` : '',
          prepaid: item.prepaid,
          categoryName: item.category?.name,
          couponName: item.product?.name,
          quantity: item.quantity,
          discount: item.discount,
          total: item.total,
          tax: item.tax,
          paymentMethod: item.paymentMethod.join(','),
          employee: item.employee.sale?.name,
        });
      });
      data.push({
        order: '',
        date: '',
        customerInfo: '',
        referenceNo: '',
        prepaid: '',
        couponName: '',
        categoryName: 'GRANT TOTAL:',
        quantity: sumQuantity,
        discount: sumDiscount.toFixed(2),
        total: sumTotal.toFixed(2),
        tax: sumTax.toFixed(2),
        paymentMethod: '',
        employee: '',
      });
      return data;
    }

    return {
      data: outOrders,
      sumDiscount,
      sumQuantity,
      sumTotal,
      sumTax,
    };
  }

  async getReportZ(
    req: CrudRequest,
    { startDate, endDate, branchIds }: ReportZQueryDTO,
  ) {
    const queryInvoices = this.invoiceRepo
      .createQueryBuilder('invoice')
      .leftJoinAndSelect('invoice.branch', 'branch')
      .leftJoinAndSelect('invoice.invoiceCoupon', 'invoiceCoupon')
      .leftJoinAndSelect('invoice.invoicePayments', 'invoicePayments')
      .leftJoinAndSelect('invoicePayments.paymentMethod', 'paymentMethod');
    if (branchIds && branchIds.length > 0) {
      queryInvoices.andWhere('branch.id IN (:...branchIds)', { branchIds });
    }
    if (
      startDate &&
      endDate &&
      moment(startDate).isValid() &&
      moment(endDate).isValid()
    ) {
      queryInvoices.andWhere('invoice.date BETWEEN :startDate AND :endDate', {
        startDate,
        endDate,
      });
    }
    const [invoices, countInvoices] = await queryInvoices.getManyAndCount();
    const firstInvoiceOfTheDay = await this.invoiceRepo
      .createQueryBuilder('invoice')
      .leftJoinAndSelect('invoice.branch', 'branch');
    if (branchIds && branchIds.length > 0) {
      firstInvoiceOfTheDay.andWhere('branch.id IN (:...branchIds)', {
        branchIds,
      });
    }
    if (
      startDate &&
      endDate &&
      moment(startDate).isValid() &&
      moment(endDate).isValid()
    ) {
      firstInvoiceOfTheDay.andWhere(
        'invoice.date BETWEEN :startDate AND :endDate',
        {
          startDate,
          endDate,
        },
      );
    }
    firstInvoiceOfTheDay.orderBy('invoice.date', 'ASC');
    const openInvoice = await firstInvoiceOfTheDay.getOne();
    const lastInvoiceOfTheDay = await this.invoiceRepo
      .createQueryBuilder('invoice')
      .leftJoinAndSelect('invoice.branch', 'branch');
    if (branchIds && branchIds.length > 0) {
      lastInvoiceOfTheDay.andWhere('branch.id IN (:...branchIds)', {
        branchIds,
      });
    }
    if (
      startDate &&
      endDate &&
      moment(startDate).isValid() &&
      moment(endDate).isValid()
    ) {
      lastInvoiceOfTheDay.andWhere(
        'invoice.date BETWEEN :startDate AND :endDate',
        {
          startDate,
          endDate,
        },
      );
    }
    lastInvoiceOfTheDay.orderBy('invoice.date', 'DESC');
    const closeInvoice = await lastInvoiceOfTheDay.getOne();

    const queryPaymentMethods =
      this.paymentMethodRepo.createQueryBuilder('paymentMethod');
    queryPaymentMethods.orderBy('paymentMethod.order', 'DESC');
    const paymentMethods = await queryPaymentMethods.getMany();

    let sumTotal = new Decimal(0);
    let sumNet = new Decimal(0);
    let sumTax = new Decimal(0);
    let sumDiscountMoney = new Decimal(0);
    let sumReceived = new Decimal(0);
    let sumTaxReceived = new Decimal(0);
    let cashCollected = new Decimal(0);
    const cashIn = new Decimal(0);
    const cashOut = new Decimal(0);
    const arrayDiscount = [];
    const arrayVoided = [];
    let arrayPaymentMethod = [];
    const arrayPaymentMethodDetail = [];
    if (paymentMethods.length) {
      for (const pmt of paymentMethods) {
        arrayPaymentMethod.push({
          code: pmt.code,
          name: pmt.name,
          value: new Decimal(0).toFixed(2),
          invoice: [],
        });
      }
    }
    for (const invoice of invoices) {
      if (invoice.voidDate) {
        arrayVoided.push({ code: invoice.code, date: invoice.voidDate });
        continue;
      }
      const paidTotal = new Decimal(invoice?.total);
      const subTotal = new Decimal(invoice?.subTotal);
      const discountMoney = new Decimal(invoice?.discount);

      const net = subTotal.minus(discountMoney);
      const tax = paidTotal.minus(net);
      sumDiscountMoney = sumDiscountMoney.plus(discountMoney);
      sumTotal = sumTotal.plus(invoice.paid);
      sumTax = sumTax.plus(tax);
      sumNet = sumNet.plus(net);
      sumTaxReceived = sumTaxReceived.plus(tax);

      for (const ipmt of invoice?.invoicePayments) {
        sumReceived = sumReceived.plus(ipmt.paid);

        const objPaymentMethod = _.find(arrayPaymentMethod, {
          code: ipmt.paymentMethod?.code,
        });
        if (objPaymentMethod) {
          objPaymentMethod.value = new Decimal(objPaymentMethod.value)
            .plus(new Decimal(ipmt.paid))
            .toFixed(2);
          objPaymentMethod.invoice.push({
            code: invoice.code,
            value: ipmt.paid.toFixed(2),
          });
        }
      }
      if (invoice.discount) {
        arrayDiscount.push({
          code: invoice.code,
          value: discountMoney.toFixed(2),
        });
      }
    }
    if (arrayPaymentMethod.length) {
      for (const pmt of arrayPaymentMethod) {
        if (!new Decimal(pmt.value).equals(0)) {
          arrayPaymentMethodDetail.push({
            name: pmt.name,
            sum: pmt.value,
            invoice: pmt.invoice,
          });
        }
        if (pmt.code === 'cash') {
          cashCollected = cashCollected.plus(new Decimal(pmt.value));
        }
      }
      arrayPaymentMethod = _.map(arrayPaymentMethod, (obj) =>
        _.omit(obj, 'invoice'),
      );
    }

    return {
      data: {
        summary: {
          saleTotal: sumTotal.toFixed(2),
          tax: sumTax.toFixed(2),
          netSale: sumNet.toFixed(2),
          received: sumReceived.toFixed(2),
          taxReceived: sumTaxReceived.toFixed(2),
          openInvoice: openInvoice?.code,
          closeInvoice: closeInvoice?.code,
          numberOfInvoices: countInvoices,
          cashCollected: cashCollected.toFixed(2),
          cashIn: cashIn.toFixed(2),
          cashOut: cashOut.toFixed(2),
          discount: {
            sumDiscount: sumDiscountMoney.toFixed(2),
            invoice: arrayDiscount,
          },
          voided: arrayVoided,
        },
        payment: {
          general: arrayPaymentMethod,
          detail: arrayPaymentMethodDetail,
        },
      },
    };
  }

  async getReportPayment(
    req: CrudRequest,
    {
      startDate,
      endDate,
      methodId,
      branchIds,
      clientZoneName,
    }: ReportPaymentQueryDTO,
    isExport = false,
  ) {
    const { page, offset, limit, sort } = req?.parsed;

    let pMethodId = [];
    if (methodId) {
      pMethodId = methodId.split(',');
    }

    const queryInvoices = this.invoiceRepo
      .createQueryBuilder('invoice')
      .leftJoinAndSelect('invoice.invoicePayments', 'invoicePayments')
      .leftJoinAndSelect('invoicePayments.paymentMethod', 'paymentMethod')
      .leftJoinAndSelect('invoice.branch', 'branch')
      .leftJoinAndSelect('invoice.customer', 'customer')
      .where('invoice.status IN (:...status)', {
        status: [InvoiceStatus.PAID, InvoiceStatus.PART_PAID],
      })
      .andWhere('invoice.status != :voidStatus', {
        voidStatus: InvoiceStatus.VOID,
      });

    if (branchIds && branchIds.length > 0) {
      queryInvoices.andWhere('branch.id IN (:...branchIds)', { branchIds });
    }

    if (
      startDate &&
      endDate &&
      moment(startDate).isValid() &&
      moment(endDate).isValid()
    ) {
      queryInvoices.andWhere('invoice.date BETWEEN :startDate AND :endDate', {
        startDate,
        endDate,
      });
    }

    if (pMethodId && pMethodId.length) {
      queryInvoices.andWhere('paymentMethod.id IN (:...pMethodId)', {
        pMethodId,
      });
    }

    queryInvoices.orderBy('invoice.date', 'ASC');
    const invoices = await queryInvoices.getMany();
    let report_order = 0;
    let sumTotal = new Decimal(0);
    let sumCharge = new Decimal(0);
    let sumDifference = new Decimal(0);
    const outInvoices = invoices.flatMap((inv) =>
      inv.invoicePayments.map((inpm) => {
        sumTotal = sumTotal.plus(inpm.paid);
        sumCharge = sumCharge.plus(0);
        sumDifference = sumDifference.plus(inpm.paid);
        if (isExport) {
          report_order++;
          return {
            order: report_order,
            date: inv.date
              ? moment.tz(inv.date, clientZoneName).format('DD/MM/YYYY h:mm A')
              : '',
            referenceNo: inv.code ? `IN${inv.code}` : '',
            payment: {
              detail: inv.note,
              method: {
                code: inpm.paymentMethod.code,
                name: inpm.paymentMethod.name,
              },
            },
            note: inv.note,
            total: inpm.paid.toFixed(2),
            charge: new Decimal(0).toFixed(2),
            difference: Number(
              parseFloat(inpm.paid.toFixed(2)) -
                parseFloat(new Decimal(0).toFixed(2)),
            ).toFixed(2),
            customer: inv.customer,
          };
        }
        return {
          date: inv.date,
          referenceNo: inv.code,
          payment: {
            detail: inpm.billCode || inv.note,
            method: {
              code: inpm.paymentMethod.code,
              name: inpm.paymentMethod.name,
            },
          },
          note: inv.note,
          total: inpm.paid.toFixed(2),
          charge: new Decimal(0).toFixed(2),
          difference: Number(
            parseFloat(inpm.paid.toFixed(2)) -
              parseFloat(new Decimal(0).toFixed(2)),
          ).toFixed(2),
          customer: inv.customer,
        };
      }),
    );

    if (!isExport) {
      return {
        data: outInvoices,
        sumTotal: sumTotal.toFixed(2),
        sumCharge: sumCharge.toFixed(2),
        sumDifference: sumDifference.toFixed(2),
      };
    }
    outInvoices.push({
      date: null,
      referenceNo: '',
      payment: {
        detail: null,
        method: {
          code: 'Total',
          name: 'GRANT TOTAL',
        },
      },
      total: sumTotal.toFixed(2),
      charge: sumCharge.toFixed(2),
      difference: sumDifference.toFixed(2),
      note: '',
      customer: null,
    });

    return {
      data: outInvoices,
    };
  }

  async getReportSaleTax(
    req: CrudRequest,
    { startDate, endDate, branchIds, clientZoneName }: ReportSaleTaxQueryDTO,
    isExport = false,
  ) {
    const queryInvoices = this.invoiceRepo
      .createQueryBuilder('invoice')
      .leftJoinAndSelect('invoice.branch', 'branch')
      .leftJoinAndSelect('invoice.invoicePayments', 'invoicePayments')
      .leftJoinAndSelect('invoicePayments.paymentMethod', 'paymentMethod')
      .where('invocie.tax = :tax', { tax: SaleTax.TAX_9 })
      .where('invoice.status IN (:...status)', {
        status: [InvoiceStatus.PAID],
      });
    if (branchIds && branchIds.length > 0) {
      queryInvoices.andWhere('branch.id IN (:...branchIds)', { branchIds });
    }
    if (
      startDate &&
      endDate &&
      moment(startDate).isValid() &&
      moment(endDate).isValid()
    ) {
      queryInvoices.andWhere('invoice.date BETWEEN :startDate AND :endDate', {
        startDate,
        endDate,
      });
    }
    queryInvoices.orderBy('invoice.date', 'ASC');
    const invoices = await queryInvoices.getMany();
    let sumNet = new Decimal(0);
    let sumTotal = new Decimal(0);
    let sumGST = new Decimal(0);
    const filterInvoices = await Promise.all(
      invoices.map(async (invoice) => {
        let net = new Decimal(0);
        let total = new Decimal(0);
        let tax = new Decimal(0);

        total = new Decimal(
          await this.getTotalSaleExceptCredits(invoice?.invoicePayments),
        );
        net = new Decimal(total.dividedBy(1.09));
        tax = new Decimal(total).minus(new Decimal(net));

        sumNet = sumNet.plus(net);
        sumTotal = sumTotal.plus(total);
        sumGST = sumGST.plus(tax);
        return {
          date: invoice.date,
          referenceNo: invoice.code,
          total: total.toFixed(2),
          tax: tax.toFixed(2),
          net: net.toFixed(2),
        };
      }),
    );

    if (isExport) {
      const data = [];
      filterInvoices.forEach((item, index) => {
        data.push({
          order: index + 1,
          date: item.date
            ? moment.tz(item.date, clientZoneName).format('DD/MM/YYYY h:mm A')
            : '',
          referenceNo: item.referenceNo ? `IN${item.referenceNo}` : '',
          total: item.total,
          tax: item.tax,
          net: item.net,
        });
      });
      data.push({
        order: '',
        date: '',
        referenceNo: 'GRANT TOTAL:',
        total: sumTotal.toFixed(2),
        tax: sumGST.toFixed(2),
        net: sumNet.toFixed(2),
      });
      return data;
    }

    return {
      data: filterInvoices,
      sumTotal: sumTotal.toFixed(2),
      sumGST: sumGST.toFixed(2),
      sumNet: sumNet.toFixed(2),
    };
  }

  async getReportTaxSummary(
    req: CrudRequest,
    { startDate, endDate, branchIds, clientZoneName }: ReportTaxSummaryQueryDTO,
    isExport = false,
  ) {
    const builderQueryInvoices = this.invoiceRepo
      .createQueryBuilder('invoice')
      .where('(invoice.created BETWEEN :startDate AND :endDate)', {
        startDate,
        endDate,
      })
      .andWhere('invoice.status IN (:...status)', {
        status: [InvoiceStatus.PAID, InvoiceStatus.PART_PAID],
      })
      .leftJoinAndSelect('invoice.invoicePayments', 'invoicePayments')
      .leftJoinAndSelect('invoicePayments.paymentMethod', 'paymentMethod')
      .groupBy(
        'invoice.created, invoice.id, invoicePayments.id, paymentMethod.id',
      )
      .orderBy('invoice.created', 'ASC');

    if (branchIds && branchIds.length > 0) {
      builderQueryInvoices.andWhere('invoice.branchId IN (:...branchIds)', {
        branchIds,
      });
    }
    const invoices = await builderQueryInvoices.getMany();

    const groupByDate = _.groupBy(invoices, 'created');
    const data = [];
    for (const key in groupByDate) {
      if (Object.prototype.hasOwnProperty.call(groupByDate, key)) {
        const element = groupByDate[key];
        let sumTotal = new Decimal(0);
        let sumNet = new Decimal(0);
        let sumTax = new Decimal(0);

        let total = new Decimal(0);
        let net = new Decimal(0);
        let tax = new Decimal(0);

        await Promise.all(
          element.map(async (inv) => {
            total = new Decimal(
              await this.getTotalSaleExceptCredits(inv.invoicePayments),
            );
            net = new Decimal(total.dividedBy(1.09));
            tax = new Decimal(total).minus(new Decimal(net));
            sumTotal = sumTotal.plus(total);
            sumNet = sumNet.plus(net);
            sumTax = sumTax.plus(tax);
          }),
        );
        data.push({
          formatted_date: moment.tz(key, clientZoneName).format('DD/MM/YYYY'),
          total: sumTotal.toFixed(2),
          net: sumNet.toFixed(2),
          tax: sumTax.toFixed(2),
        });
      }
    }

    const groupByDateSum = _.groupBy(data, 'formatted_date');
    const dataSum = [];
    for (const key in groupByDateSum) {
      if (Object.prototype.hasOwnProperty.call(groupByDateSum, key)) {
        const element = groupByDateSum[key];
        let sumTotal = new Decimal(0);
        let sumNet = new Decimal(0);
        let sumTax = new Decimal(0);
        element.forEach((inv) => {
          sumTotal = sumTotal.plus(inv.total);
          sumNet = sumNet.plus(inv.net);
          sumTax = sumTax.plus(inv.tax);
        });
        dataSum.push({
          formatted_date: key,
          total: sumTotal.toFixed(2),
          net: sumNet.toFixed(2),
          tax: sumTax.toFixed(2),
        });
      }
    }
    let sumTotal = new Decimal(0);
    let sumNet = new Decimal(0);
    let sumTax = new Decimal(0);
    dataSum.forEach((item) => {
      sumTotal = sumTotal.plus(item.total);
      sumNet = sumNet.plus(item.net);
      sumTax = sumTax.plus(item.tax);
    });

    if (isExport) {
      const dataExport = [];
      dataSum.forEach((item, index) => {
        dataExport.push({
          order: index + 1,
          date: item.formatted_date,
          total: item.total,
          net: item.net,
          tax: item.tax,
        });
      });
      dataExport.push({
        order: '',
        date: 'GRANT TOTAL:',
        total: sumTotal.toFixed(2),
        net: sumNet.toFixed(2),
        tax: sumTax.toFixed(2),
      });
      return dataExport;
    }

    return {
      data: dataSum,
      sumTotal: sumTotal.toFixed(2),
      sumNet: sumNet.toFixed(2),
      sumGST: sumTax.toFixed(2),
    };
  }

  async getExportDailySale(
    req: CrudRequest,
    branchIds: string[],
    currencyCode: string,
  ) {
    const [transactionSummary, paymentSummary, customerSummary] =
      await Promise.all([
        this.salesService.getTransactionSummary(req, branchIds),
        this.salesService.getPaymentSummary(req, branchIds),
        this.salesService.getCustomerSummary(req, branchIds),
      ]);

    const exportData = [
      { title: 'Transaction Summary', value: '' },
      { title: 'TOTAL SALES', value: `GROSS TOTAL (${currencyCode})` },
      ...Object.values(transactionSummary).map((item) => ({
        title: item.items === 'sum' ? 'Total sales' : item.items,
        value: `${currencyCode} ${item.total.toFixed(2)}`,
      })),
      { title: ' ', value: ' ' },
      { title: 'Payment Summary', value: '' },
      { title: 'PAYMENT TYPE', value: `PAYMENT COLLECTED (${currencyCode})` },
      ...Object.values(paymentSummary).map(
        (item: { items: string; total: number }) => ({
          title: item.items,
          value: `${currencyCode} ${item.total.toFixed(2)}`,
        }),
      ),
      { title: ' ', value: ' ' },
      { title: 'Customer Summary', value: '' },
      { title: 'STATUS', value: 'TOTAL' },
      ...Object.values(customerSummary).map((item) => ({
        title: item.items === 'sum' ? 'Total customer' : item.items,
        value: item.total,
      })),
    ];

    return exportData;
  }

  async calculateDiscountForItem(
    orderItemId: string | UUID,
    items: Array<OrderDetail>,
    totalDiscount: number,
  ) {
    let totalValue = 0;
    items.forEach((item) => {
      if (!item.product.isNotApplyDiscount) {
        totalValue += item.product.price * item.quantity;
      }
    });

    let discountPerItem = 0;
    if (totalValue > 0) {
      discountPerItem = totalDiscount / totalValue;
    }

    let result = 0;
    items.forEach((item) => {
      if (item.id === orderItemId && !item.product.isNotApplyDiscount) {
        const itemTotalValue = item.product.price * item.quantity;
        result = itemTotalValue * discountPerItem;
      }
    });
    return result;
  }

  async calculateTaxForItem(
    orderItemId: string | UUID,
    items: Array<OrderDetail>,
    invoicePayments: Array<InvoicePaymentMethod>,
    totalDiscount: number,
    taxRate = 0.09,
  ): Promise<number> {
    const totalPaid = invoicePayments
      .filter(
        (payment) =>
          payment.paymentMethod.code !== CreditType.NEW &&
          payment.paymentMethod.code !== CreditType.OLD,
      )
      .reduce(
        (sum, payment) => sum + payment.paid + (payment?.roundNumber || 0),
        0,
      );
    const totalPaidByCredit = invoicePayments
      .filter(
        (payment) =>
          payment.paymentMethod.code == CreditType.NEW ||
          payment.paymentMethod.code == CreditType.OLD,
      )
      .reduce((sum, payment) => sum + payment.paid, 0);

    const totalInvoiceAmount = totalPaid + totalPaidByCredit;
    if (totalPaid === 0) {
      return 0;
    }

    const nonCreditRatio = totalPaid / totalInvoiceAmount;

    let tax = 0;
    for (const item of items) {
      if (item.id === orderItemId) {
        const itemTotalValue = item.product.price * item.quantity;

        if (!item.product.isNotApplyDiscount && totalDiscount > 0) {
          const discountForItem = await this.calculateDiscountForItem(
            orderItemId,
            items,
            totalDiscount,
          );

          const itemValueAfterDiscount = itemTotalValue - discountForItem;

          const itemNonCreditValue =
            (itemValueAfterDiscount * Math.trunc(nonCreditRatio * 100)) / 100;
          tax = this.roundToTwoDecimalPlaces(itemNonCreditValue * taxRate);
        } else {
          const itemNonCreditValue = itemTotalValue * nonCreditRatio;
          tax = this.roundToTwoDecimalPlaces(itemNonCreditValue * taxRate);
        }
      }
    }
    return tax;
  }

  async getTotalSaleExceptCredits(
    invoicePayments: Array<InvoicePaymentMethod>,
  ) {
    let total = 0;
    invoicePayments.forEach((payment) => {
      if (
        payment.paymentMethod.code !== CreditType.NEW &&
        payment.paymentMethod.code !== CreditType.OLD
      ) {
        total += payment.paid + (payment?.roundNumber || 0);
      }
    });
    return total;
  }

  public roundToTwoDecimalPlaces(value) {
    return Math.round(value * 100) / 100;
  }

  public async getReportFoodAndBeverageSale(
    req: CrudRequest,
    {
      startDate,
      endDate,
      branchIds,
      clientZoneName,
      categoryId,
      categoryBeverageId,
      keySearch,
      limit,
      page,
    }: ReportFoodAndBeverageDailySaleQueryDTO,
    isExport = false,
  ) {
    const queryBuilder = this.invoiceRepo
      .createQueryBuilder('invoice')
      .leftJoinAndSelect('invoice.parentInvoice', 'parentInvoice')
      .leftJoinAndSelect('invoice.orders', 'orders')
      .leftJoinAndSelect('orders.items', 'items')
      .leftJoinAndSelect('items.product', 'product')
      .leftJoinAndSelect('product.category', 'category')
      .leftJoinAndSelect('invoice.invoiceCoupon', 'invoiceCoupon')
      .leftJoinAndSelect('invoice.invoicePayments', 'invoicePayments')
      .leftJoinAndSelect('invoicePayments.paymentMethod', 'paymentMethod')
      .where('invoice.status IN (:...statuses)', {
        statuses: [InvoiceStatus.PAID, InvoiceStatus.PART_PAID],
      })
      .andWhere(
        new Brackets((qb) => {
          qb.where('product.type = :food', { food: ProductType.FOOD }).orWhere(
            'product.type = :beverage',
            { beverage: ProductType.BEVERAGE },
          );
        }),
      );

    if (branchIds.length > 0) {
      queryBuilder.andWhere('invoice.branchId IN (:...branchIds)', {
        branchIds,
      });
    }

    queryBuilder.andWhere('invoice.date BETWEEN :startDate AND :endDate', {
      startDate,
      endDate,
    });

    if (categoryId || categoryBeverageId) {
      const allCategoryIds = [
        ...(categoryId ? categoryId.split(',').map((id) => id.trim()) : []),
        ...(categoryBeverageId
          ? categoryBeverageId.split(',').map((id) => id.trim())
          : []),
      ];

      queryBuilder.andWhere('category.id IN (:...allCategoryIds)', {
        allCategoryIds,
      });
    }

    if (keySearch) {
      queryBuilder.andWhere(
        new Brackets((qb) => {
          qb.where('product.name = :keySearch', { keySearch });
        }),
      );
    }

    queryBuilder.orderBy('invoice.date', 'ASC');

    const qLimit = getCustomPaginationLimit(limit);
    const offset = (page - 1) * limit || 0;
    if (!isExport) {
      queryBuilder.take(qLimit).skip(offset);
    }

    const [invoices, total] = await queryBuilder.getManyAndCount();

    let report_order = 0;
    let sumQuantity = 0;
    let sumDiscount = new Decimal(0);
    let sumTotal = new Decimal(0);
    let sumTax = new Decimal(0);

    const outInvoices = invoices.flatMap((inv) => {
      const allItems = inv.orders.flatMap((order) => order.items);
      const invoiceDiscount = new Decimal(inv.discount || 0);
      const invoiceCoupon =
        Array.isArray(inv.invoiceCoupon) && inv.invoiceCoupon.length > 0
          ? inv.invoiceCoupon[0]
          : null;

      // Calculate total eligible items value for proportional discount
      const eligibleItems = allItems.filter(
        (item) => !item.product?.isNotApplyDiscount,
      );
      const eligibleTotalBeforeTax = eligibleItems.reduce(
        (sum, item) =>
          sum.plus(new Decimal(item.price || 0).times(item.quantity || 0)),
        new Decimal(0),
      );
      const allDetailDiscountZero = allItems.every(
        (item) => !item.discount || item.discount === 0,
      );

      return allItems.map((item) => {
        report_order++;
        const unitPrice = new Decimal(item.price || 0);
        const quantity = new Decimal(item.quantity || 0);
        let unitDiscount = new Decimal(0);

        // Calculate discount
        if (allDetailDiscountZero && invoiceDiscount.gt(0) && unitPrice.gt(0)) {
          if (!item.product?.isNotApplyDiscount) {
            if (
              invoiceCoupon &&
              invoiceCoupon.couponType === 'Percentage' &&
              invoiceCoupon.percent
            ) {
              unitDiscount = unitPrice.times(invoiceCoupon.percent).div(100);
            } else {
              if (eligibleTotalBeforeTax.gt(0)) {
                unitDiscount = invoiceDiscount.times(
                  unitPrice.div(eligibleTotalBeforeTax),
                );
              }
            }
          }
        } else {
          unitDiscount = new Decimal(item.discount || 0);
        }

        const totalDiscount = unitDiscount;
        const totalPrice = unitPrice;
        const lineTotalBeforeTax = totalPrice;

        const totalPaidByCredit = inv.invoicePayments
          .filter(
            (payment) =>
              payment.paymentMethod.code === CreditType.NEW ||
              payment.paymentMethod.code === CreditType.OLD,
          )
          .reduce((sum, payment) => sum + payment.paid, 0);

        const totalNonCreditPaid = inv.invoicePayments
          .filter(
            (payment) =>
              payment.paymentMethod.code !== CreditType.NEW &&
              payment.paymentMethod.code !== CreditType.OLD,
          )
          .reduce(
            (sum, payment) =>
              sum + (payment.paid + (payment?.roundNumber || 0)),
            0,
          );

        // Calculate base amount before tax
        const baseAmount = totalPrice.minus(totalDiscount);

        // Calculate ratio of non-credit payment to total payment
        const nonCreditRatio =
          totalNonCreditPaid / (totalNonCreditPaid + totalPaidByCredit);

        // Adjust base amount and calculate tax for non-credit portion only
        const adjustedBaseAmount = baseAmount.times(nonCreditRatio);
        const taxAmount = adjustedBaseAmount.gt(0)
          ? adjustedBaseAmount.times(0.09)
          : new Decimal(0);

        const creditPaidPercentage = totalPaidByCredit / inv.subTotal;

        // Calculate total excluding credit payment portion
        const total = totalPrice
          .minus(totalPrice.times(creditPaidPercentage))
          .minus(totalDiscount)
          .plus(taxAmount);

        // Update running totals
        sumQuantity += quantity.toNumber();
        sumDiscount = sumDiscount.plus(totalDiscount);
        sumTotal = sumTotal.plus(total);
        sumTax = sumTax.plus(taxAmount);

        return {
          order: report_order,
          date: inv.date
            ? moment.tz(inv.date, clientZoneName).format('DD/MM/YYYY h:mm A')
            : '',
          referenceNo: inv.code ? `IN${inv.code}` : '',
          customer: inv.payload['customer']
            ? `${inv.payload['customer']['code']} ${inv.payload['customer']['firstName']} ${inv.payload['customer']['lastName']}`
            : 'N/A',
          items: item.product ? item.product.name : '',
          type: item.product?.type || 'N/A',
          category: item.product?.category?.name || 'N/A',
          qty: quantity.toNumber(),
          price: unitPrice.toFixed(2),
          discount: totalDiscount.toFixed(2),
          total: total.toFixed(2),
          tax: taxAmount.toFixed(2),
          subtotal: lineTotalBeforeTax.toFixed(2),
          paymentMethod:
            inv.payload['invoicePayments']?.length > 0
              ? inv.payload['invoicePayments']
                  .map((p) => p.paymentMethod?.name || 'N/A')
                  .join(', ')
              : 'N/A',
          saleEmployee: inv.payload['referral']
            ? inv.payload['referral']['fullname']
            : 'N/A',
        };
      });
    });

    if (isExport) {
      return [
        ...outInvoices,
        {
          order: '',
          date: '',
          referenceNo: '',
          customer: '',
          items: '',
          type: '',
          category: 'GRAND TOTAL:',
          qty: sumQuantity,
          price: '',
          discount: sumDiscount.toFixed(2),
          total: sumTotal.toFixed(2),
          tax: sumTax.toFixed(2),
          paymentMethod: '',
          saleEmployee: '',
        },
      ];
    }

    return {
      ...createPaginationReportInfo(
        outInvoices,
        total,
        qLimit || total,
        offset || 0,
      ),
      sumQuantity: sumQuantity,
      sumDiscount: sumDiscount.toFixed(2),
      sumTotal: sumTotal.toFixed(2),
      sumTax: sumTax.toFixed(2),
    };
  }

  public async getReportSaleProfit(
    req: CrudRequest,
    { startDate, endDate, branchIds, limit, page }: ReportSaleProfitQueryDTO,
    isExport = false,
  ) {
    // Get individual invoices with payment details to calculate non-credit amounts
    const queryBuilder = this.invoiceRepo
      .createQueryBuilder('invoice')
      .leftJoinAndSelect('invoice.invoicePayments', 'invoicePayments')
      .leftJoinAndSelect('invoicePayments.paymentMethod', 'paymentMethod')
      .leftJoinAndSelect('invoice.orders', 'orders')
      .leftJoinAndSelect('orders.items', 'orderDetail')
      .leftJoinAndSelect('orderDetail.product', 'product')
      .leftJoin(
        'invoice.childInvoices',
        'childInvoice',
        'childInvoice.status = :voidStatus',
      )
      .where('invoice.status IN (:...statuses)', {
        statuses: [InvoiceStatus.PAID, InvoiceStatus.PART_PAID],
      })
      .andWhere('invoice.date BETWEEN :startDate AND :endDate', {
        startDate,
        endDate,
      })
      .setParameter('voidStatus', InvoiceStatus.VOID);

    if (branchIds.length > 0) {
      queryBuilder.andWhere('invoice.branchId IN (:...branchIds)', {
        branchIds,
      });
    }

    queryBuilder.orderBy('invoice.date', 'ASC');

    const invoices = await queryBuilder.getMany();

    // Group invoices by date and calculate adjusted values
    const dateGroups = new Map();

    for (const invoice of invoices) {
      const dateKey = moment(invoice.date).format('YYYY-MM-DD');

      if (!dateGroups.has(dateKey)) {
        dateGroups.set(dateKey, {
          date: dateKey,
          tax: 0,
          grosssale: 0,
          discount: 0,
          paymentcharge: 0,
          netsales: 0,
          cost: 0,
          profit: 0,
          refund: 0,
        });
      }

      const dayData = dateGroups.get(dateKey);

      // Calculate non-credit payments
      const totalNonCreditPaid = invoice.invoicePayments
        .filter(
          (payment) =>
            payment.paymentMethod.code !== CreditType.NEW &&
            payment.paymentMethod.code !== CreditType.OLD,
        )
        .reduce((sum, payment) => {
          return sum + (payment.paid + (payment?.roundNumber || 0));
        }, 0);

      const totalCreditPaid = invoice.invoicePayments
        .filter(
          (payment) =>
            payment.paymentMethod.code === CreditType.NEW ||
            payment.paymentMethod.code === CreditType.OLD,
        )
        .reduce((sum, payment) => {
          return sum + payment.paid;
        }, 0);

      const totalPaid = totalNonCreditPaid + totalCreditPaid;

      // Calculate non-credit ratio
      const nonCreditRatio = totalPaid > 0 ? totalNonCreditPaid / totalPaid : 0;

      // For GROSS SALES: Use subTotal (pre-discount, pre-tax) and prorate by non-credit ratio
      const adjustedGrossSales = (invoice.subTotal || 0) * nonCreditRatio;

      // For DISCOUNT: Prorate by non-credit ratio
      const adjustedDiscount = (invoice.discount || 0) * nonCreditRatio;

      // For NET SALES: Prorate by non-credit ratio
      const adjustedNetSales = adjustedGrossSales - adjustedDiscount;

      // For TAX: Calculate based on prorated net sales
      const adjustedTax = adjustedNetSales * 0.09; // Assuming 9% tax

      // Calculate cost from products
      let totalCost = 0;
      if (invoice.orders && invoice.orders.length > 0) {
        for (const order of invoice.orders) {
          if (order.items && order.items.length > 0) {
            for (const item of order.items) {
              const itemCost = (item.product?.cost || 0) * (item.quantity || 0);
              totalCost += itemCost;
            }
          }
        }
      }

      // For cost: Prorate by non-credit ratio
      const finalCost = totalCost * nonCreditRatio;

      // Calculate profit (net sales - cost)
      const adjustedProfit = adjustedNetSales - finalCost;

      // Add refund from child invoices (if any)
      const refundAmount = invoice.childInvoices
        ? invoice.childInvoices
            .filter((child) => child.status === InvoiceStatus.VOID)
            .reduce((sum, child) => sum + (child.total || 0), 0)
        : 0;

      // Update day totals

      dayData.tax += adjustedTax;
      dayData.grosssale += adjustedGrossSales;
      dayData.discount += adjustedDiscount;
      dayData.netsales += adjustedNetSales;
      dayData.cost += finalCost;
      dayData.profit += adjustedProfit;
      dayData.refund += refundAmount;
    }

    // Convert map to array and sort by date
    const result = Array.from(dateGroups.values()).sort((a, b) =>
      a.date.localeCompare(b.date),
    );

    // Apply pagination if not exporting
    const totalRecords = result.length;
    let paginatedResult = result;

    if (!isExport) {
      const qLimit = getCustomPaginationLimit(limit);
      const offset = (page - 1) * limit || 0;
      paginatedResult = result.slice(offset, offset + qLimit);
    }

    const summary = {
      sumTax: 0,
      sumGrossSale: 0,
      sumRefund: 0,
      sumDiscount: 0,
      sumPaymentCharge: 0,
      sumNetSales: 0,
      sumCost: 0,
      sumProfit: 0,
      sumSalesMargin: 0,
    };

    // Calculate summary from all results (not just paginated) and add salesmargin
    result.forEach((row) => {
      // Add salesmargin property to each row
      (row as any).salesmargin =
        row.netsales > 0 ? (row.profit / row.netsales) * 100 : 0;

      summary.sumTax += row.tax;
      summary.sumGrossSale += row.grosssale;
      summary.sumRefund += row.refund;
      summary.sumDiscount += row.discount;
      summary.sumNetSales += row.netsales;
      summary.sumCost += row.cost;
      summary.sumProfit += row.profit;
    });

    summary.sumSalesMargin =
      summary.sumNetSales > 0
        ? (summary.sumProfit / summary.sumNetSales) * 100
        : 0;

    if (isExport) {
      return [
        ...result.map((row) => ({
          ...row,
          tax: row.tax.toFixed(2),
          grossSale: row.grosssale.toFixed(2),
          refund: row.refund.toFixed(2),
          discount: row.discount.toFixed(2),
          netSales: row.netsales.toFixed(2),
          cost: row.cost.toFixed(2),
          profit: row.profit.toFixed(2),
          salesMargin: (row as any).salesmargin.toFixed(2),
        })),
        {
          date: 'GRAND TOTAL:',
          tax: summary.sumTax.toFixed(2),
          grossSale: summary.sumGrossSale.toFixed(2),
          refund: summary.sumRefund.toFixed(2),
          discount: summary.sumDiscount.toFixed(2),
          netSales: summary.sumNetSales.toFixed(2),
          cost: summary.sumCost.toFixed(2),
          profit: summary.sumProfit.toFixed(2),
          salesMargin: summary.sumSalesMargin.toFixed(2),
        },
      ];
    }

    const qLimit = getCustomPaginationLimit(limit);
    const offset = (page - 1) * limit || 0;

    return {
      ...createPaginationReportInfo(
        paginatedResult,
        totalRecords,
        qLimit,
        offset,
      ),
      ...summary,
    };
  }
}
